<?php
declare(strict_types=1);

namespace MadHat\CanonicalUrl\Plugin\Framework\View\Page;

use Magento\Framework\View\Page\Config;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\App\ObjectManager;

class ConfigPlugin
{
    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * @var Configurable
     */
    private Configurable $configurableProduct;

    /**
     * @param StoreManagerInterface $storeManager
     * @param RequestInterface|null $request
     * @param ProductRepositoryInterface|null $productRepository
     * @param Configurable|null $configurableProduct
     */
    public function __construct(
        StoreManagerInterface $storeManager,
        ?RequestInterface $request = null,
        ?ProductRepositoryInterface $productRepository = null,
        ?Configurable $configurableProduct = null
    ) {
        $this->storeManager = $storeManager;
        $this->request = $request ?: ObjectManager::getInstance()->get(RequestInterface::class);
        $this->productRepository = $productRepository ?: ObjectManager::getInstance()->get(ProductRepositoryInterface::class);
        $this->configurableProduct = $configurableProduct ?: ObjectManager::getInstance()->get(Configurable::class);
    }

    public function aroundAddRemotePageAsset(
        Config $subject,
        callable $proceed,
        string $url,
        string $contentType,
        array $attributes = []
    ) {
        // Proceed normally if not modifying a canonical URL
        if ($contentType !== 'canonical') {
            return $proceed($url, $contentType, $attributes);
        }

        // Check if this is a product page
        $fullActionName = $this->request->getFullActionName();
        if ($fullActionName === 'catalog_product_view') {
            return $this->handleProductCanonicalUrl($subject, $proceed, $url, $contentType, $attributes);
        }

        // Handle B2B store URL transformation for non-product pages
        $currentStore = $this->storeManager->getStore();
        $storeCode = $currentStore->getCode();

        if ($storeCode === 'b2b') {
            try {
                $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
                $currentBaseUrl = $currentStore->getBaseUrl();

                $url = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $url);
            } catch (\Exception $e) {
                // Store retrieval issue, do nothing
            }
        }

        return $proceed($url, $contentType, $attributes);
    }

    /**
     * Handle canonical URL for product pages
     *
     * @param Config $subject
     * @param callable $proceed
     * @param string $url
     * @param string $contentType
     * @param array $attributes
     * @return mixed
     */
    protected function handleProductCanonicalUrl(
        Config $subject,
        callable $proceed,
        string $url,
        string $contentType,
        array $attributes = []
    ) {
        $productId = (int)$this->request->getParam('id');
        if (!$productId) {
            // No product ID, proceed with default behavior
            return $proceed($url, $contentType, $attributes);
        }

        try {
            $product = $this->productRepository->getById($productId);

            // Check if this is a variant based on URL pattern or product attributes
            if ($this->isVariantProduct($product)) {
                // This is a variant - return self-canonical URL
                $currentUrl = $this->getCurrentUrl();
                return $proceed($currentUrl, $contentType, $attributes);
            }

            // This is a base product - no canonical link
            return null;

        } catch (NoSuchEntityException $e) {
            // Product not found, proceed with default behavior
            return $proceed($url, $contentType, $attributes);
        }
    }

    /**
     * Get current URL with store transformation if needed
     *
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getCurrentUrl(): string
    {
        $currentUrl = $this->request->getUri()->__toString();

        // Apply store URL transformation if needed (same logic as other pages)
        $currentStore = $this->storeManager->getStore();
        $storeCode = $currentStore->getCode();

        if ($storeCode === 'b2b') {
            try {
                $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
                $currentBaseUrl = $currentStore->getBaseUrl();
                $currentUrl = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $currentUrl);
            } catch (\Exception $e) {
                // Store retrieval issue, do nothing
            }
        }

        return $currentUrl;
    }

    /**
     * Determine if a product is a variant based on custom logic
     *
     * @param \Magento\Catalog\Api\Data\ProductInterface $product
     * @return bool
     */
    protected function isVariantProduct($product): bool
    {
        $currentUrl = $this->request->getRequestUri();

        // Check if this is a variant based on URL pattern
        // Base products typically have "base" in their URL
        if (strpos($currentUrl, '-base-') !== false) {
            return false;
        }

        // If URL contains specific variant indicators (color, size, weight, etc.), it's likely a variant
        $variantIndicators = ['-tpu-', '-pla-', '-abs-', '-petg-', '-mm-', '-g-', '-yellow-', '-blue-', '-red-', '-green-', '-black-', '-white-', '-grey-', '-gray-'];
        foreach ($variantIndicators as $indicator) {
            if (strpos($currentUrl, $indicator) !== false) {
                return true;
            }
        }

        // If URL is very long with many segments, it's likely a variant
        $hyphenCount = substr_count($currentUrl, '-');
        if ($hyphenCount > 8) {
            return true;
        }

        // For configurable products, check if we're accessing via a variant URL
        if ($product->getTypeId() === 'configurable') {
            // If the URL doesn't contain "base" but the product is configurable,
            // it might be accessed via a variant URL
            $pathInfo = trim($this->request->getPathInfo(), '/');
            if (strpos($pathInfo, 'base') === false && $hyphenCount > 5) {
                return true;
            }
        }

        // Traditional variant check for simple products
        if ($product->getTypeId() === 'simple') {
            $parentIds = $this->configurableProduct->getParentIdsByChild($product->getId());
            if (!empty($parentIds)) {
                return true;
            }
        }

        // Default to base product
        return false;
    }
}
