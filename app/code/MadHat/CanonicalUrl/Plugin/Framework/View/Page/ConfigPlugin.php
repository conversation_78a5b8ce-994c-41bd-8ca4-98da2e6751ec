<?php
declare(strict_types=1);

namespace MadHat\CanonicalUrl\Plugin\Framework\View\Page;

use Magento\Framework\View\Page\Config;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\App\ObjectManager;
use Psr\Log\LoggerInterface;

class ConfigPlugin
{
    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * @var ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;

    /**
     * @var Configurable
     */
    private Configurable $configurableProduct;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @param StoreManagerInterface $storeManager
     * @param RequestInterface|null $request
     * @param ProductRepositoryInterface|null $productRepository
     * @param Configurable|null $configurableProduct
     * @param LoggerInterface|null $logger
     */
    public function __construct(
        StoreManagerInterface $storeManager,
        ?RequestInterface $request = null,
        ?ProductRepositoryInterface $productRepository = null,
        ?Configurable $configurableProduct = null,
        ?LoggerInterface $logger = null
    ) {
        $this->storeManager = $storeManager;
        $this->request = $request ?: ObjectManager::getInstance()->get(RequestInterface::class);
        $this->productRepository = $productRepository ?: ObjectManager::getInstance()->get(ProductRepositoryInterface::class);
        $this->configurableProduct = $configurableProduct ?: ObjectManager::getInstance()->get(Configurable::class);
        $this->logger = $logger ?: ObjectManager::getInstance()->get(LoggerInterface::class);
    }

    public function aroundAddRemotePageAsset(
        Config $subject,
        callable $proceed,
        string $url,
        string $contentType,
        array $attributes = []
    ) {
        // Proceed normally if not modifying a canonical URL
        if ($contentType !== 'canonical') {
            return $proceed($url, $contentType, $attributes);
        }

        // Check if this is a product page
        $fullActionName = $this->request->getFullActionName();
        if ($fullActionName === 'catalog_product_view') {
            return $this->handleProductCanonicalUrl($subject, $proceed, $url, $contentType, $attributes);
        }

        // Handle B2B store URL transformation for non-product pages
        $currentStore = $this->storeManager->getStore();
        $storeCode = $currentStore->getCode();

        if ($storeCode === 'b2b') {
            try {
                $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
                $currentBaseUrl = $currentStore->getBaseUrl();

                $url = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $url);
            } catch (\Exception $e) {
                // Store retrieval issue, do nothing
            }
        }

        return $proceed($url, $contentType, $attributes);
    }

    /**
     * Handle canonical URL for product pages
     *
     * @param Config $subject
     * @param callable $proceed
     * @param string $url
     * @param string $contentType
     * @param array $attributes
     * @return mixed
     */
    protected function handleProductCanonicalUrl(
        Config $subject,
        callable $proceed,
        string $url,
        string $contentType,
        array $attributes = []
    ) {
        $productId = (int)$this->request->getParam('id');
        if (!$productId) {
            // No product ID, proceed with default behavior
            return $proceed($url, $contentType, $attributes);
        }

        try {
            $product = $this->productRepository->getById($productId);

            // Check if this is a variant based on URL pattern or product attributes
            if ($this->isVariantProduct($product)) {
                // This is a variant - return self-canonical URL
                $currentUrl = $this->getCurrentUrl();
                return $proceed($currentUrl, $contentType, $attributes);
            }

            // This is a base product - no canonical link
            return null;

        } catch (NoSuchEntityException $e) {
            // Product not found, proceed with default behavior
            return $proceed($url, $contentType, $attributes);
        }
    }

    /**
     * Get current URL with store transformation if needed
     *
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getCurrentUrl(): string
    {
        $currentUrl = $this->request->getUri()->__toString();

        // Apply store URL transformation if needed (same logic as other pages)
        $currentStore = $this->storeManager->getStore();
        $storeCode = $currentStore->getCode();

        if ($storeCode === 'b2b') {
            try {
                $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
                $currentBaseUrl = $currentStore->getBaseUrl();
                $currentUrl = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $currentUrl);
            } catch (\Exception $e) {
                // Store retrieval issue, do nothing
            }
        }

        return $currentUrl;
    }

    /**
     * Determine if a product is a variant based on custom logic
     *
     * @param \Magento\Catalog\Api\Data\ProductInterface $product
     * @return bool
     */
    protected function isVariantProduct($product): bool
    {
        // First check traditional configurable product relationship
        if ($product->getTypeId() === 'simple') {
            $parentIds = $this->configurableProduct->getParentIdsByChild($product->getId());
            if (!empty($parentIds)) {
                return true; // Traditional variant
            }
        }

        // Check if this is a variant based on URL pattern
        // Base products typically have "base" in their URL key or are shorter/simpler
        $currentUrl = $this->request->getRequestUri();
        $urlKey = $product->getUrlKey();

        // If URL contains "base", it's likely a base product
        if (strpos($currentUrl, '-base-') !== false || strpos($urlKey, '-base-') !== false) {
            return false;
        }

        // If URL is very long with specific attributes (like color, size, weight), it's likely a variant
        // Count the number of hyphens as a simple heuristic
        $hyphenCount = substr_count($currentUrl, '-');
        if ($hyphenCount > 8) { // Adjust this threshold as needed
            return true;
        }

        // Check if product has specific variant attributes that indicate it's a variant
        $variantAttributes = ['madhat_color', 'madhat_weight', 'madhat_filament_size'];
        foreach ($variantAttributes as $attributeCode) {
            $attributeValue = $product->getData($attributeCode);
            if (!empty($attributeValue)) {
                return true; // Has variant-specific attributes
            }
        }

        // Default to base product
        return false;
    }
}
